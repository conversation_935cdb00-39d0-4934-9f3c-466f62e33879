@import "../../templates/loading/threepoint.wxss";
page{
  background-color: #f5f7f7;
}
/* 顶部Tab菜单 */

.tab-box {
  width: 100%;
  height: 80rpx;
  background: #fff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9996;
  box-shadow: 0 3px 6px -2px rgba(0,0,0,.1);
}

 

.tab-menu {
  width: 570rpx;
  height: 80rpx;
  margin: 0 auto;
  white-space: nowrap;
  z-index: 9997;
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 88rpx;
}

.tab-menu-item {
  display: inline-block;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 15px;
  font-weight: 500;
  color: #999;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.tab-menu .tab-menu-item:last-child {
  padding-right: 10rpx;
}

/* 付费专栏提示 */

.pay-tip {
  position: relative;
}

.pay-tip::after {
  position: absolute;
  top: -10rpx;
  right: -30rpx;
  content: '付费';
  font-size: 8px;
  line-height: 16px;
  color: #fff;
  background: rgba(255, 107, 53, 0.8);
  border-radius: 8px;
  height: 16px;
  padding: 0 8rpx;
}

.tab-menu-item.active {
  color: #FF6B35;
  font-weight: 600;
  position: relative;
}

.tab-menu-item.active:after {
  display: block;
  height: 4rpx;
  width: 28rpx;
  content: '';
  background: #FF6B35;
  border-radius: 2rpx;
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translate(-50%, 0);
}

/*顶部Tab两侧的搜索和下拉图标*/

.tab-search-btn {
  height: 80rpx;
  width: 80rpx;
  padding: 20rpx;
  box-sizing: border-box;
  position: absolute;
  left: 10rpx;
  top: 0;
  font-size: 22px;
  color: #777;
  line-height: 1;
}

.tab-videolink-btn {
  height: 80rpx;
  width: 80rpx;
  padding: 20rpx;
  box-sizing: border-box;
  position: absolute;
  left: 60rpx;
  top: 0;
}


.tab-custom-btn {
  height: 80rpx;
  width: 80rpx;
  padding: 20rpx;
  box-sizing: border-box;
  position: absolute;
  right: 20rpx;
  top: 0;
  z-index: 998;
  font-size: 22px;
  color: #777;
  line-height: 1;
}

.tab-custom-btn-open {
  transform-origin: center center;
  transform: rotateZ(180deg);
  position: fixed;
  background: #fff;
}

/* 点击下拉图标弹出的面板样式 */

.columnPannelHeader {
  position: fixed;
  background: #fff;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 660rpx;
}

.columnPannelTitle {
  font-size: 14px;
  line-height: 88rpx;
  padding: 0 30rpx;
  height: 88rpx;
  white-space: nowrap;
  box-sizing: border-box;
}

.columnPannel {
  width: 100%;
  height: 100%;
  position: relative;
  top: 0;
  left: 0;
  box-sizing: border-box;
  background: #fff;
  padding-top: 74rpx;
  z-index: 9998;
}

.selected-pannel, .unselected-pannel {
  padding: 10rpx 0 0 30rpx;
  font-size: 16px;
}

.column-item-selected, .column-item-unselected {
  display: inline-block;
  position: relative;
  width: 150rpx;
  margin: 15rpx;
  border: 1rpx solid #ddd;
  text-align: center;
  border-radius: 15rpx;
  font-size: 28rpx;
  white-space: nowrap;
  box-sizing: border-box;
}

.column-item-selected-first {
  color: #999;
}

.column-item-selected-active  {  
  color: #FF6B35;
  font-weight: 600;
}

.column-item-selected-text {
  max-width: 100%;
  overflow: hidden;
}

/* 下拉列表里“列表展示方式”样式 */

.settings-container {
  margin: 0;
}

.section-choice {
  margin-bottom: 72rpx;
}

.section-choice radio-group {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.section__title {
  font-size: 26rpx;
  color: #959595;
}

.section-choice label {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 100rpx;
  font-size: 28rpx;
  color: #333;
  margin-right: 80rpx;
  padding-left: 15rpx;
}

/* 首页内容区 */

.index-container {
  margin-top: 80rpx;
}
.index_top{
  background-color: #fff;
}
/* 轮播图 */
.swiper-container{
  position: relative;
  padding-top: 24rpx;
}

.swiper-box {
  position: relative;
  height: 395rpx;
  margin: 24rpx;
  margin-top:0 ;
  border-radius: 4px;
  overflow: hidden;
}

.swiper-image {
  display: block; 

  width: 100%;
  height: 100%; 
  background: #f5f7f7;   
}

.swiper-item {
  height: 100%;
  width: 100%;
  box-sizing: border-box; 
}

swiper .swiper-desc { 
  position: absolute;
  bottom: 0;
  width: 100%;
  color: #fff;
  font-weight: 500;
  height: 180rpx; 
  margin: 0 auto;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 32rpx;
  box-sizing: border-box;
  padding: 120rpx 24rpx 0 24rpx;
  border-radius: 0px 0px, 4px, 4px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
}
 
.points {
  padding: 0 32rpx;
}

.points .img {
  width: 100%;
  height: 220rpx;
}
.dots-container{
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  bottom: 24rpx;
  right: 48rpx; 
  /* background-color: gray; */
}
.dot{
  height:8rpx;
  width: 8rpx;
  background-color: #fff;
  border-radius: 50%;
  margin-left: 8rpx;
  transition: all .25s;
  box-sizing: border-box;
 
}
.dot.active{
  width: 24rpx;
  border-radius: 12px;
  background: rgba(255, 107, 53, 0.8);
}
.dot:nth-of-type(1){
  margin-left: 0;
}
/* 模块标题样式 */

.common-subtitle {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  margin-top: 24rpx;
  padding: 0 24rpx; 
}

.common-subtitle-left {
  font-size: 18px;
  color: #333;
  font-weight: 600;
}

.common-subtitle-right {
  font-size: 26rpx;
  font-weight: 400;
  color: #959595;
  margin-right: 6rpx;
}

/* 精选栏目菜单导航 */

.selected-nav {
  border-bottom: 16rpx solid #f5f7f7;
}

.selected-nav-list {
  display: flex;
   padding:0 24rpx 24rpx;
  background: #fff;
}

.selected-nav-item {
  margin-right: 16rpx;
  text-align: center;
}

.selected-nav-item image {
  width: 156rpx;
  height: 156rpx;
  border-radius: 6rpx;
  background: #f5f7f7;
}

.selected-nav-item text {
  line-height: 26rpx;
  font-size: 26rpx;
  color: #333;
}

/* 热门标签 */
.tags {
  border-bottom: 16rpx solid #f5f7f7;
  background: #fff;
}

.tagsname-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: 0 40rpx;
  padding-bottom: 16rpx;
}

.tagsname {
  height: 72rpx;
  border-radius: 36rpx;
  background: #f5f7f7;
  padding: 0 36rpx;
  margin-right: 24rpx;
  margin-bottom: 24rpx;
}

.tagsname-name {
  color: #4c4c4c;
  font-size: 26rpx;
  line-height: 72rpx;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/*  最新评论  */

.new-comments {
  padding: 0 40rpx 10rpx;
  background: #fff;
  margin-top: -30rpx;
  border-bottom: 16rpx solid #f5f7f7;
}

.new-comments-item {
  display: flex;
  align-items: center;
  background: #fff;
}

.new-comments-item image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  box-shadow: 4px 10px 30px -8px rgba(139, 161, 185, 0.9);
}

.new-comments-right {
  width: 100%;
  height: 100%;
  padding: 30rpx 0;
  margin-left: 40rpx;
  border-bottom: 1rpx solid #eee;
}

.new-comments-item:last-child .new-comments-right {
  border-bottom: none;
}

.new-comments-right>text {
  font-size: 28rpx;
  font-weight: 500;
}

.new-comments-content {
  font-size: 14px;
  line-height: 1.6;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin: 10rpx 0;
}

.new-comments-des {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #959595;
  font-size: 24rpx;
}

.new-comments-des>text {
  height: 40rpx;
  line-height: 40rpx;
  padding: 0 16rpx;
  border-radius: 20rpx;
  background: #f5f7f7;
}

/* 发布文章 */

.addarticle {
  /* width: 110rpx;
  height: 110rpx;
  padding-top: 15rpx;
  position: fixed;
  bottom: 160rpx;
  right: 16rpx;
  z-index: 999; */
  position: fixed;
  bottom: 30rpx;
  right: 16rpx;
  width: 100rpx;
  height: 100rpx;
  background: #FFF;
  border-radius: 50%;
  box-shadow: 2rpx 4rpx 10rpx rgba(0, 0, 0, .1);
  text-align: center;
  z-index: 999;
}

.addarticle .icon-pen {
  font-size: 40rpx;
  line-height: 100rpx;
}

.topic-list-item {
  position: relative;
  overflow: hidden;
  clear: both;
  margin-bottom: 30rpx;
  background: #fff;
}

.topic-list-item  image.cover {
  width: 100%;
  height: 400rpx;
  filter: brightness(90%);
}

.topic-content-information {
  position: absolute;
  z-index: 2;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  vertical-align: middle;
  width: 90%;
}

.topic-content-title {
  font-size: 48rpx;
  font-weight: 500;
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 8rpx;
}

.topic-content-num {
  font-size: 24rpx;
  line-height: 24rpx;
  color: #fff;
  margin-bottom: 24rpx;
}

.topic-content-btn button {
  width: 140rpx;
  height: 56rpx;
  font-size: 26rpx;
  line-height: 46rpx;
  font-weight: 500;
  color: #fff;
  border: 4rpx #fff solid;
  border-radius: 28rpx;
  text-align: center;
  vertical-align: middle;
}

.me-item-gap {
  width: 690rpx;
  height: 60rpx;
  background-color: #fff;
  margin-left: 30rpx;
}

.me-item-gap-title {
  width: 100rpx;
  height: 1rpx;
  background-color: #eee;
  margin-left: 24rpx;
}

.btn-submit::after {
  display: none;
}

.btn-submit {
  width: 638rpx;
  height: 88rpx;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  background-color: #FF6B35;
  margin-bottom: 24rpx;
  line-height: 88rpx;
}

.btn-cancel::after {
  display: none;
}

.btn-cancel {
  width: 638rpx;
  height: 88rpx;
  border-radius: 0;
  border: 2rpx solid #eee;
  font-size: 14px;
  font-weight: 500;
  color: #959595;
  background-color: #fff;
  line-height: 88rpx;
}

@-webkit-keyframes refreshAnimation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes refreshAnimation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 媒体合集 */
/* .media-center {
  border-bottom: 16rpx solid #f5f7f7;
} */

.media-content {
  display: grid;
  display: -ms-grid;
  /* #if MP */
  grid-template-columns: 280rpx 197rpx 197rpx;
  /* #elif IOS */
  grid-template-columns: 280rpx 197rpx 197rpx;
  /* #elif ANDROID */
  grid-template-columns: 30rpx 300rpx 300rpx;
  /* #endif */
  grid-template-rows: 112rpx 112rpx;
  grid-row-gap: 16rpx;
  grid-column-gap: 16rpx;
   padding:0 24rpx 24rpx;
}

.media-content .media-item {
  border-radius: 6rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 14px;
}

.media-content .media-item.video {
  grid-column-start: 1;
  grid-column-end: 2;
  grid-row-start: 1;
  grid-row-end: 3;
  background: linear-gradient(to right, #FF6B35, #FFB74D);
  font-size: 16px;
}

.media-content .media-item.video .icon-media-video {
  font-size: 68rpx;
}

.media-content .media-item.img-text {
  grid-column-start: 2;
  grid-column-end: 3;
  grid-row-start: 1;
  grid-row-end: 2;
  background: linear-gradient(to right, #FFB74D, #FF6B35);
}
.media-content .media-item.album {
  grid-column-start: 3;
  grid-column-end: 4;
  grid-row-start: 1;
  grid-row-end: 2;
  background: linear-gradient(to right, #FF6B35, #E65100);
}
.media-content .media-item.audio {
  grid-column-start: 2;
  grid-column-end: 3;
  grid-row-start: 2;
  grid-row-end: 3;
  background: linear-gradient(to right, #FFB74D, #FF6B35);
}
.media-content .media-item.file {
  grid-column-start: 3;
  grid-column-end: 4;
  grid-row-start: 2;
  grid-row-end: 3;
  background: linear-gradient(to right, #FF6B35, #E65100);
}

/* 引导添加到我的小程序 */

.addMyMiniapp {
  color: #fff;
  line-height: 1.2;
  background: #FF6B35;
  padding: 24rpx 24rpx;
  border-radius: 12rpx;
  position: fixed;
  top: 20rpx;
  right: 24rpx;
  z-index: 9999;
  box-shadow: 0 16rpx 30rpx -12rpx rgba(88, 88, 88, 0.2);
}

.addMyMiniapp>view {
  font-size: 24rpx;
  font-weight: 500;
}

.addMyMiniapp>text {
  font-size: 20rpx;
  font-weight: 350;
}

.addMyMiniapp::before {
  content: '';
  width: 20rpx;
  height: 20rpx;
  background: #FF6B35;
  transform: rotate(45deg);
  position: absolute;
  right: 118rpx;
  top: -10rpx;
}

/* 公告 */
.notice {
  display: flex;
  height: 80rpx;
     
  overflow: hidden;
  padding:0 24rpx; 
  margin:  36rpx 0;
  font-size: 15px;
  color: #333;
}
.notice_img{
  width: 80rpx;
  height: 80rpx;
  margin-right: 12rpx;
}
.notice > .iconfont {
  font-size: 28rpx;
  margin-right: 6rpx;
  color: #999;
}

.notice-swiper {
  display: flex;
  margin-left: 5rpx;
  flex: 1;
  height: 80rpx;
  overflow: hidden;
}

.notice-swiper-item {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;    
  height: 40rpx!important;
  line-height: 45rpx;
} 
/* 订阅 */

.subscribe {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #999;
  background: #f5f7f7;
  padding: 30rpx 40rpx; 
}

.subscribe > view {
  height: 60rpx;
  padding: 0 24rpx;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
  line-height: 60rpx;
  border-radius: 40rpx;
  background: #FF6B35;
}

.subscribe-card {
  background: rgba(230, 250, 236, 1);
  border-radius: 8rpx;
  font-size: 15px;
  color: #333;
  padding: 24rpx;    
}

.subscribe-wrap {
  /* border-bottom: 1px solid #EEE; */
  /* display: flex;
  justify-content: space-between;
  align-items: center; */
  /* padding: 50rpx 30rpx; */
  /* background: rgba(230, 250, 236, 1);
  border-radius: 16rpx; */
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* .subscribe-wrap::before {
  content: '';
  position: absolute;
  bottom: -190rpx;
  left: -60rpx;
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  background: #e4f2f2;
  z-index: 1;
}

.subscribe-wrap::after {
  content: '';
  position: absolute;
  bottom: -60rpx;
  right: -60rpx;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: #e3f1ee;
  z-index: 1;
} */

.subscribe-tip,
.subscribe-info {
  position: relative;
  z-index: 2;
}

.subscribe-wrap .small {
  font-size: 12px;
  color: #666;
}

.subscribe-wrap .subscribe-btn {
  display: inline-block;
   font-size: 12px;
  line-height: 1;
  /* color: #07c160;
  border: 1px solid #07c160; */
  /* border-radius: 6rpx; */
  padding: 8rpx 12rpx;
  /* margin-left: 16rpx; */
  margin-top: 8rpx;
  flex-shrink: 0;
  /* height: 60rpx;
  padding: 0 24rpx; */
  border-radius: 6rpx;
  background: #FF6B35;
  color: #FFF;
}

.official-account official-account {
  border: none;
}

.icon-close:before {
  content: '';
  width: 30rpx;
  height: 4rpx;
  background: #666;
  transform: rotate(45deg);
  position: absolute;
  top: 10rpx;
  right: 0;
}

.icon-close:after {
  content: '';
  width: 30rpx;
  height: 4rpx;
  background: #666;
  transform: rotate(-45deg);
  position: absolute;
  top: 10rpx;
  right: 0;
}

.pupop-scroll-view {
  max-height: 800rpx;
}

.pupop-body {
  min-width: 600rpx;
}

.pupop-body > .item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
}

.pupop-body > .item:not(:last-child) {
  border-bottom: 1rpx solid #eee;
}

.pupop-body > .item > text {
  font-size: 15px;
  color: #333;
}

.pupop-body > .item > .btn-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pupop-body > .item > .btn-box text {
  font-size: 12px;
  color: #999;
}

.pupop-body > .item > .btn-box view {
  height: 60rpx;
  padding: 0 30rpx;
  font-size: 12px;
  color: #fff;
  line-height: 60rpx;
  border-radius: 30rpx;
  background: #FF6B35;
  margin-left: 6rpx;
}

/* 视频号 */
.video-chennel {
  padding: 24rpx;
  font-size: 14px;
  color: #333;
  border-bottom: 16rpx solid #F5F7F7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.video-chennel .btn {
  height: 60rpx;
  padding: 0 32rpx;
  border-radius: 32rpx;
  border: 1rpx solid #FF6B35;
  line-height: 60rpx;
  color: #FF6B35;
}

.pupop-insert {
  background: #fff;
  padding: 48rpx 30rpx 30rpx;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
}

.pupop-insert > .header {
  position: relative;
  text-align: center;
  margin-bottom: 40rpx;
}

.pupop-insert > .header text {
  font-size: 20px;
  font-weight: 500;
}

.pupop-insert > .header > .icon-close {
  height: 48rpx;
  width: 48rpx;
  position: absolute;
  top: 0;
  right: 0;
}

.icon-close:before {
  content: '';
  width: 30rpx;
  height: 4rpx;
  background: #666;
  transform: rotate(45deg);
  position: absolute;
  top: 10rpx;
  right: 0;
}

.icon-close:after {
  content: '';
  width: 30rpx;
  height: 4rpx;
  background: #666;
  transform: rotate(-45deg);
  position: absolute;
  top: 10rpx;
  right: 0;
}

.pupop-body > .content {
  margin: 30rpx 20rpx 60rpx;
}

.pupop-body > .content > view {
  font-size: 16px;
  color: #333;
  line-height: 2;
}

.pupop-body > .content > view > text {
  font-weight: 500;
}

.pupop-body .content .btn-copy {
  height: 72rpx;
  width: 200rpx;
  border-radius: 36rpx;
  background: #FF6B35;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  color: #fff;
  line-height: 72rpx;
  margin: 40rpx auto 20rpx;
}
.official-account{
  margin: 24rpx;
}

.grid-box {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 24rpx;
  grid-row-gap: 24rpx;
  padding:0 24rpx  24rpx 24rpx  ;
  /* border-bottom: 16rpx solid #f5f7f7; */
}

.point {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: rgba(220, 224, 255, 0.5);
  border-radius: 8rpx;
  color: #333;
  line-height: 1;
  height: 100%;
  box-sizing: border-box;
}

.point-title {
  font-size: 15px;
}

.point-des {
  font-size: 10px;
  color: #666;
  margin-top: 16rpx;
}

.point-img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: rgba(255,255,255,.8);
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.point-img .iconfont {
  font-size: 40rpx;
  font-weight: 100;
  color: #FFB74D;
  line-height: 1;
}
.list-container{
  margin: 24rpx;

}