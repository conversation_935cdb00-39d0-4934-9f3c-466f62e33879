/*
 * 微慕小程序
 * author: jianbo
 * organization:  微慕 www.minapper.com 
 * 技术支持微信号：Jianbo
 * Copyright (c) 2018 https://www.minapper.com All rights reserved.
 */

const API = require('../utils/api.js')
import { dountwxlogin } from '../utils/new/api.js'

const Auth = {}
Auth.wxLogin = function () {
  return new Promise(function (resolve, reject) {
    // #if MP
    wx.login({
      success: function (res) {
        let args = {};
        args.js_code = res.code;
        resolve(args);
      },
      fail: function (err) {
        reject(err);
      }
    })
    // #endif
  })
}

Auth.userLogin = function (args, api) {
  // let a = args || {}
  // let u = a.userInfo || {}
  // if (u.nickName =="微信用户")
  // {
  //    u.nickName='';
  //    u.avatarUrl='';
  // } 
  // a.userInfo = u
  return new Promise(function (resolve, reject) {
    api.wxUserLogin(args).then(res => {
      const user = res.userInfo || {}
      res.userInfo = {
        ...user,
        //avatarUrl: user.avatarurl,
        //nickName: user.nickname
      }
      resolve(res) 
    })
  })
}

Auth.scopeUserInfo = function () {
  return new Promise(function (resolve, reject) {
    let args = {};
    wx.getSetting({
      success: function success(res) {
        var authSetting = res.authSetting;
        if (!('scope.userInfo' in authSetting)) {
          args.scopeUserInfo = 'none';
        } else {
          if (authSetting['scope.userInfo'] === false) {
            args.scopeUserInfo = '0';
          }
          else {
            args.scopeUserInfo = '1';
          }
        }
        resolve(args.scopeUserInfo);
      },
      fail: function (err) {
        reject(err);
      }
    })
  })
}

Auth.agreeGetUser = function (e, api, wxLoginInfo, authFlag) {
  return new Promise(function (resolve, reject) {
    // #if MP
    let args = {};
    args.js_code = wxLoginInfo.js_code;

    // 先检查隐私协议
    wx.requirePrivacyAuthorize({
      success: () => {
        // 隐私协议已同意，继续获取用户信息
        wx.getUserProfile({
          lang: 'zh_CN',
          desc: '登录后信息展示',
          success: (res) => {
            let _userInfo = res.userInfo || {}
            args.userInfo = _userInfo
            wx.showLoading({
              title: "正在登录...",
              mask: true
            })
            Auth.userLogin(args, api).then(res => {
              wx.hideLoading();
              var result={};
              if (res.raw_session) {
                result.userSession = res.raw_session;
                result.errcode = "";
              }
              if(res.code) {
                result.errcode = res.code;
                result.message = res.message;
              }
              var userInfo=res.userInfo
              userInfo.isLogin=true;
              result.userInfo = {
                ...userInfo,
              }
              resolve(result)
            })
          },
          fail: (err) => {
            wx.hideLoading()
            if (err.errMsg=='getUserProfile:fail auth deny') {
              err.errMsg= '用户拒绝了授权'
            } else if (err.errMsg === 'getUserProfile:fail privacy permission is not authorized') {
              err.errMsg = '您未同意用户隐私协议，将无法登录本小程序'
            }
            wx.showToast({
              icon: 'none',
              title: err.errMsg || '登录错误，请稍后再试',
            })
            var result={};
            result.errcode = err.errMsg;
            resolve(result)
          }
        })
      },
      fail: () => {
        // 隐私协议未同意
        wx.showToast({
          icon: 'none',
          title: '请先同意隐私协议',
        })
        var result={};
        result.errcode = '隐私协议未同意';
        resolve(result)
      }
    });
    // #elif NATIVE
    // wx.checkIdentitySession({
    //   success: () => {
    //     wx.getIdentityCode({
    //       success: (res) => {
    //         if (res.code) {
    //           Auth.dountWxAppLogin(res.code).then(curRes => {
    //             resolve(curRes)
    //           })
    //         } else {
    //           wx.weixinAppLogin({
    //             success: async (response) => {
    //               if (response.code) {
    //                 Auth.dountWxAppLogin(response.code).then(curRes => {
    //                   resolve(curRes)
    //                 })
    //               } else {
    //                 wx.showToast({
    //                   icon: 'none',
    //                   title: '登录错误，请稍后再试',
    //                 })
    //               }
    //             },
    //             fail: err => {
    //               wx.showToast({
    //                 icon: 'none',
    //                 title: err.errMsg || '登录错误，请稍后再试',
    //               })
    //             }
    //           })
    //         }
    //       },
    //       fail: () => {
    //         wx.weixinAppLogin({
    //           success: async (response) => {
    //             if (response.code) {
    //               Auth.dountWxAppLogin(response.code).then(curRes => {
    //                 resolve(curRes)
    //               })
    //             } else {
    //               wx.showToast({
    //                 icon: 'none',
    //                 title: '登录错误，请稍后再试',
    //               })
    //             }
    //           },
    //           fail: err => {
    //             wx.showToast({
    //               icon: 'none',
    //               title: err.errMsg || '登录错误，请稍后再试',
    //             })
    //           }
    //         })
    //       }
    //     })
    //   },
    //   fail: () => {
    //     // console.log('系统登录态失效')
    //     wx.weixinAppLogin({
    //       success: async (response) => {
    //         if (response.code) {
    //           Auth.dountWxAppLogin(response.code).then(curRes => {
    //             resolve(curRes)
    //           })
    //         } else {
    //           wx.showToast({
    //             icon: 'none',
    //             title: '登录错误，请稍后再试',
    //           })
    //         }
    //       },
    //       fail: err => {
    //         wx.showToast({
    //           icon: 'none',
    //           title: err.errMsg || '登录错误，请稍后再试',
    //         })
    //       }
    //     })
    //   }
    // }
    const { type } = e.target.dataset
    if (['1', '2'].includes(type)) {
      wx.miniapp.hasWechatInstall({
        success: (res) => {
          getAppLoginCode(type).then(res => resolve(res))
        },
        fail: (err) => {
          if (err.errMsg === 'hasWechatInstall:fail 开发者工具暂时不支持此 API 调试，请使用真机进行开发') {
            getAppLoginCode(type).then(res => resolve(res))
            return
          }
          wx.showToast({
            icon: 'none',
            title: '需登录微信授权，请先安装微信客户端完成授权',
          })
        }
      })
    } else {
      getAppLoginCode(type).then(res => resolve(res))
    }
    // #endif
  })
}

function getAppLoginCode(type) {
  return new Promise(resolve => {
    let apiName = 'weixinMiniProgramLogin'
    if (type === '2') apiName = 'weixinAppLogin'
    if (type === '3') apiName = 'appleLogin'

    wx[apiName]({
      success: (response) => {
        if (response.code) {
          Auth.dountWxAppLogin({
            code: response.code,
            type
          }).then(curRes => {
            resolve(curRes)
          })
        } else {
          wx.showToast({
            icon: 'none',
            title: '登录错误，请稍后再试',
          })
        }
      },
      fail: err => {
        if (['1', '2'].includes(type) && err.errCode === -700000 && err.errMsg.includes('fail: -700000.sendOpenReq failed:fail opensdk failed')) {
          // {errCode: -700000, errMsg: "wx.weixinMiniProgramLogin fail: -700000.sendOpenReq failed:fail opensdk failed 并没有通过微信返回多端App。"}
          // 用户小程序或微信授权登录跳转微信不授权直接返回app，前端报错提示，苹果审核登录会被拒绝
          return
        }
        wx.showToast({
          icon: 'none',
          title: err.errMsg || '登录错误，请稍后再试',
        })
      }
    })
  })
}

Auth.dountWxAppLogin = async function ({ code, type }) {
  return new Promise(async (resolve, reject) => {
    wx.showLoading({
      title: '正在登录...',
      mask: true
    })
    const res = await dountwxlogin({ code, type })
    wx.hideLoading()

    // code 错误时重新获取 https://dev.weixin.qq.com/docs/framework/dev/openapi/code2Verifyinfo.html
    // if (res.code === 10001001) {
    //   wx.weixinAppLogin({
    //     success: async (response) => {
    //       if (response.code) {
    //         Auth.dountWxAppLogin(response.code).then(curRes => {
    //           resolve(curRes)
    //         })
    //       } else {
    //         wx.showToast({
    //           icon: 'none',
    //           title: '登录错误，请稍后再试',
    //         })
    //       }
    //     },
    //     fail: err => {
    //       wx.showToast({
    //         icon: 'none',
    //         title: err.errMsg || '登录错误，请稍后再试',
    //       })
    //     }
    //   })
    //   return
    // }

    if (res.raw_session) {
      const info =  {
        userSession: res.raw_session,
        errcode: res.code || '',
        message: res.message,
        userInfo: {
          ...(res.userInfo || {}),
          isLogin: true
        }
      }
      resolve(info)
    } else {
      wx.showToast({
        icon: 'none',
        title: '登录失败，请稍后再试',
      })
    }
  })
}

Auth.getMemberUserInfo = function (args, api) {
  return new Promise(function (resolve, reject) {
    let weixinUsreInfo = {};
    api.getMemberUserInfo(args).then(res => {
      resolve(res);
    })
  })
}

Auth.getUserInfo = function (args, api) {
  return new Promise(function (resolve, reject) {
    api.getUserInfo(args).then(res => {
      resolve(res);
    })
  })
}

Auth.checkLogin = function (appPage) {
  let wxLoginInfo = wx.getStorageSync('wxLoginInfo');  
  wx.checkSession({
    success: function () {
      if (!wxLoginInfo.js_code) {
        Auth.wxLogin().then(res => {
          if(appPage)
          {
            appPage.setData({ wxLoginInfo: res });
          }
          
          wx.setStorageSync('wxLoginInfo', res);
        })
      }
    },
    fail: function () {
      Auth.wxLogin().then(res => {
        if(appPage)
        {
          appPage.setData({ wxLoginInfo: res });
        }
        
        wx.setStorageSync('wxLoginInfo', res);
      })
    }
  })
}

Auth.checkSession = function (app, api, appPage, flag, util) {
  //Auth.checkLogin(appPage); 
  let userSession = wx.getStorageSync('userSession');
  if (!userSession.sessionId) {
    var userInfo = { avatarUrl: "../../images/gravatar.png", nickName: "立即登录", isLogin: false }
    appPage.setData({userInfo: userInfo });
    if ('isLoginNow' == flag) {      
      appPage.setData({ isLoginPopup: true});
    }
  }
  else {

    if (util.checkSessionExpire(userSession.sessionExpire)) {
      var data = {};
      data.userId = userSession.userId;
      data.sessionId = userSession.sessionId;
      api.updateSession(data).then(res => {
        if (res.raw_session) {
          wx.setStorageSync('userSession', res.raw_session);
          Auth.setUserMemberInfoData(appPage);
        }
        else if (res.code == 'user_parameter_error') {
          Auth.logout(appPage);
          wx.reLaunch({
            url: '../index/index'
          })
        }
      })
    }
    else {
      Auth.setUserMemberInfoData(appPage);
    }
  }
}

Auth.checkGetMumber = function (app, appPage, api) {
  let memberUserInfo = wx.getStorageSync('memberUserInfo');
  let userSession = wx.getStorageSync('userSession');
  if (userSession.sessionId && !memberUserInfo.membername) {
    Auth.getMemberUserInfo(userSession, api).then(res => {
      if (res.memberUserInfo) {
        appPage.setData({ memberUserInfo: res.memberUserInfo });
        wx.setStorageSync('memberUserInfo', res.memberUserInfo);
      }
    })
  }
}

Auth.checkAgreeGetUser = function (e, app, appPage, api, authFlag) {
  // #if MP
  let wxLoginInfo = wx.getStorageSync('wxLoginInfo');
  if (wxLoginInfo.js_code) {
    Auth.agreeGetUser(e, api, wxLoginInfo, authFlag).then(res => {
      console.log(res);
      if (res.errcode == "") {
        appPage.setData({ userInfo: res.userInfo });
        wx.setStorageSync('userInfo', res.userInfo);
        wx.setStorageSync('userSession', res.userSession);
        appPage.setData({ userSession: res.userSession,isLoginPopup: false  });
       
        Auth.getMemberUserInfo(res.userSession, api).then(response => {
          if (response.memberUserInfo) {
            appPage.setData({ memberUserInfo: response.memberUserInfo });
            wx.setStorageSync('memberUserInfo', response.memberUserInfo);
            if (appPage.data.pageName == "detail" || appPage.data.pageName == "media-list" || appPage.data.pageName == "social" || appPage.data.pageName == "sociallist") {
              appPage.onPullDownRefresh();
            }
            else if(appPage.data.pageName == "earnIntegral" )  
            {
              appPage.initData();
              appPage.setData({isLogin:true});
            }          
          }
        })
      }
      else {
        var userInfo = { avatarUrl: "../../images/gravatar.png", nickName: "点击登录", isLogin: false }
        appPage.setData({ userInfo: userInfo ,isLoginPopup: false  })
        wx.z.showDialog({
          type: "confirm",
          title: "提示",
          showTitle: false,
          confirmText: "确认",
          content: '登录失败,重新登录?',
          success: (res) => {        
              Auth.logout(appPage);            
              Auth.checkLogin(appPage); 
          }
        })
      }
    })
  }
  else {
    wx.showToast({
      title: '登录失败',
      mask: false,
      duration: 1000
    });
  }
  // #elif NATIVE
  Auth.agreeGetUser(e).then(res => {
    appPage.setData({ userInfo: res.userInfo });
    wx.setStorageSync('userInfo', res.userInfo);
    wx.setStorageSync('userSession', res.userSession);
    appPage.setData({ userSession: res.userSession,isLoginPopup: false  });

    Auth.getMemberUserInfo(res.userSession, api).then(response => {
      if (response.memberUserInfo) {
        appPage.setData({ memberUserInfo: response.memberUserInfo });
        wx.setStorageSync('memberUserInfo', response.memberUserInfo);
        if (appPage.data.pageName == "detail" || appPage.data.pageName == "media-list" || appPage.data.pageName == "social" || appPage.data.pageName == "sociallist") {
          appPage.onPullDownRefresh();
        }
        else if(appPage.data.pageName == "earnIntegral" )
        {
          appPage.initData();
          appPage.setData({isLogin:true})
        }
      }
    })
  })
  // #endif
}

Auth.checkGetMemberUserInfo = function (userSession, appPage, api) {
  if (userSession.sessionId) {
    Auth.getMemberUserInfo(userSession, api).then(res => {
      if (res.memberUserInfo) {
        var userInfo =appPage.data.userInfo;
        userInfo.nickName=res.memberUserInfo.nickname;
        userInfo.avatarUrl=res.memberUserInfo.avatarurl;
        userInfo.enableUpdateAvatarCount=res.memberUserInfo.enableUpdateAvatarCount;
        appPage.setData({ memberUserInfo: res.memberUserInfo,userInfo:userInfo });
        wx.setStorageSync('memberUserInfo', res.memberUserInfo);
        
      } else {
        if (appPage.data.pageName == 'myself') {
          wx.showToast({
            title: res.message,
            icon: "none",
            duration: 3000
          });
        }
      }
      if (appPage.data.isPull) {
        wx.stopPullDownRefresh()
      }
    })
  }
}
Auth.setUserMemberInfoData = function (appPage) {
  appPage.setData({ articleStyle: wx.getStorageSync('articleStyle') });  
  let curUserInfo = wx.getStorageSync('userInfo')
  if (!curUserInfo.avatarUrl || !curUserInfo.nickName) {
    curUserInfo = {
      avatarUrl: "../../images/gravatar.png",
      nickName: "立即登录",
      isLogin: false
    }
    if(appPage.data.pageName =="myself" || appPage.data.pageName =="earnIntegral" )
    {
      appPage.setData({ isLoginPopup: true });
    }
   
    return;
  }
  let userSession = wx.getStorageSync('userSession');
  let wxLoginInfo = wx.getStorageSync('wxLoginInfo');          
  let memberUserInfo = wx.getStorageSync('memberUserInfo');
  if(userSession.sessionId && !memberUserInfo.membername)
  {

    API.getMemberUserInfo(userSession).then(res => {
      if (res.memberUserInfo) {          
        memberUserInfo=res.memberUserInfo;
        wx.setStorageSync('memberUserInfo', res.memberUserInfo);
      }
      
    })
    
  }
  appPage.setData({
    userInfo: curUserInfo,
    userSession: userSession,
    wxLoginInfo: wxLoginInfo,
    memberUserInfo: memberUserInfo
  })
  
}

Auth.logout = function (appPage) {
  appPage.setData({
    userSession: {},
    memberUserInfo: {},
    userInfo: { avatarUrl: "../../images/gravatar.png", nickName: "立即登录", isLogin: false },
    wxLoginInfo: {}
  })
  wx.removeStorageSync('userInfo');
  wx.removeStorageSync('userSession');
  wx.removeStorageSync('memberUserInfo');
  wx.removeStorageSync('wxLoginInfo');
  Auth.checkLogin(appPage);
}

Auth.getPhoneMumber = function (appPage, api, iv, encryptedData) {
  return new Promise(function (resolve, reject) {
    Auth.wxLogin().then(res => {
      appPage.setData({ wxLoginInfo: res });
      wx.setStorageSync('wxLoginInfo', res);
      let args = {};
      let data = {};
      args.js_code = res.js_code;
      args.iv = iv;
      args.encryptedData = encryptedData;
      api.getPhoneMumber(args).then(res => {
        if (res.code != 'error') {
          data.phoneinfo = res.phoneinfo;
          data.errcode = "";
          data.message = res.message;
          resolve(data);
        } else {
          data.errcode = "error";
          data.phoneNumber = "";
          data.message = res.message;
          resolve(data);
        }
      })
    })
  })
}

Auth.getUserPhoneMumber = function (e,api) {
  return new Promise(function (resolve, reject) {
   
      let userSession = wx.getStorageSync('userSession');
      let userId=userSession.userId;
      let sessionId=userSession.sessionId;  
      let args = {};
      let data = {};
      args.code =e.detail.code;
      args.userid = userId;
      args.sessionid = sessionId;
      api.getUserPhoneMumber(args).then(res => {
        if (res.code != 'error') {
          data.phoneinfo = res.phoneinfo;
          data.errcode = "";
          data.message = res.message;
          resolve(data);
        } else {
          data.errcode = "error";
          data.phoneNumber = "";
          data.message = res.message;
          resolve(data);
        }
      })
    })

}

module.exports = Auth