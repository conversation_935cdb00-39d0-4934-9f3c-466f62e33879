<!--
/*
 * 
 * 微慕小程序
 * author: jianbo
 * organization:  微慕 www.minapper.com 
 * 技术支持微信号：Jianbo
 * Copyright (c) 2018 https://www.minapper.com All rights reserved.
 *
 */-->

<template name="login">  

  <!-- 内容 -->
  <view class="login-info">
    <view class="login-phone">
      <text class="iconfont icon-login-phone" />
    </view>
    <view class="login-title">- 授权体验更多功能 -</view>
    <view class="login-tip">
      <text class="iconfont icon-login-like" />评论点赞，参与文章动态互动</view>
    <view class="login-tip">
      <text class="iconfont icon-login-social" />发布动态，吸引粉丝关注好友</view>
    <view class="login-tip">
      <text class="iconfont icon-shipping-bag" />购买商品，签到获取积分奖励</view>
    <view class="login-tip">...</view>


    <!-- 按钮 -->
    <view class="login-footer">
      <!-- #if MP -->
      <button class="login-btn-ok" open-type="agreePrivacyAuthorization" bindagreeprivacyauthorization="agreeGetUser">立即登录</button>
      <view class="login-btn-cancel" catchtap="closeLoginPopup">暂不登录</view>
      <!-- #elif ANDROID -->
      <button class="login-btn-ok" data-type="1" catchtap="agreeGetUser">小程序登录</button>
      <view class="login-btn-cancel"  data-type="2" catchtap="agreeGetUser"><text class="iconfont icon-weixin" data-type="2" />微信登录</view>
      <!-- #elif IOS -->
      <block wx:if="{{hasWechatInstall}}">
        <button class="login-btn-ok" data-type="1" catchtap="agreeGetUser">小程序登录</button>
        <view class="login-btn-box">
          <view class="btn-wechat" data-type="2" catchtap="agreeGetUser"><text class="iconfont icon-weixin" data-type="2" />微信登录</view>
          <view class="btn-apple" data-type="3" catchtap="agreeGetUser"><text class="iconfont icon-apple" data-type="3" />苹果登录</view>
        </view>
      </block>
      <block wx:else>
        <button class="login-btn-ok" data-type="3" catchtap="agreeGetUser">苹果登录</button>
        <view class="login-btn-cancel" catchtap="closeLoginPopup">暂不登录</view>
      </block>
      <!-- #endif -->
    </view>
  </view>
</template>