const app = getApp()
const Auth = require('../../utils/auth.js')

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: false
  },
  properties: {
    show: {
      type: Boolean,
      value: false
    }
  },
  data: {
    code: '',
    success: null,
    hasWechatInstall: false
  },

  /**
  * 组件的初始数据
  */
  attached() {
    this.initDialog()
  },

  pageLifetimes: {
    show() {
      this.initDialog()
    },
  },

  methods: {
    // 初始化弹窗
    initDialog() {
      // ios 检测是否有安装微信，未安装时隐藏掉微信登录
      // #if NATIVE
      wx.miniapp.hasWechatInstall({
        success: (res) => {
          this.setData({
            hasWechatInstall: res.hasWechatInstall
          })
        },
        fail: (err) => {
          this.setData({
            hasWechatInstall: err.errMsg === 'hasWechatInstall:fail 开发者工具暂时不支持此 API 调试，请使用真机进行开发'
          })
        }
      })
      // #endif

      // 获取code
      var code=this.data.code;
      var self=this;
      wx.checkSession({
        success: function () {
          if (!code) {
            // #if MP
            wx.login({
              success: (r) => {
                if (r.code) {
                  self.setData({
                    code: r.code
                  })
                }
              }
            })
            // #endif
          }
        },
        fail: function () {
          // #if MP
          wx.login({
            success: (r) => {
              if (r.code) {
                self.setData({
                  code: r.code
                })
              }
            }
          })
          // #endif
        }
      })

      

      const config = {
        success: null
      }

      wx.z = wx.z || {}
      wx.z.showLogin = (options) => {
        const {
          success = config.success
        } = options

        this.setData({
          show: true,
          success
        })
        return this
      }
    },

    // 登录：获取用户信息
    onConfirmTap(e) {
      // #if MP
      // 先检查隐私协议
      wx.requirePrivacyAuthorize({
        success: () => {
          // 隐私协议已同意，继续获取用户信息
          wx.getUserProfile({
            lang: 'zh_CN',
            desc: '登录后信息展示',
            success: (res) => {
              console.log(res)
              let userInfo = res.userInfo || {}
              userInfo.isLogin = true;
              this.onLogin(userInfo)
            },
            fail: (err) => {
              if (err.errMsg=='getUserProfile:fail auth deny') {
                err.errMsg= '用户拒绝了授权'
              } else if (err.errMsg === 'getUserProfile:fail privacy permission is not authorized') {
                err.errMsg = '您未同意用户隐私协议，将无法登录本小程序'
              }
              wx.showToast({
                icon: 'none',
                title: err.errMsg || '登录错误，请稍后再试',
              })
            }
          })
        },
        fail: () => {
          // 隐私协议未同意
          wx.showToast({
            icon: 'none',
            title: '请先同意隐私协议',
          })
        }
      })
      // #elif NATIVE
      wx.checkIdentitySession({
        success: (res) => {
          console.log(res)
          // console.log('系统登录态生效')
          wx.getIdentityCode({
            success: (r) => {
              console.log(r)
              if (r.code) {
                this.dountLogin(r.code)
              } else {
                this.getWeixinAppLogin()
              }
            },
            fail: () => {
              this.getWeixinAppLogin()
            }
          })
        },
        fail: () => {
          // console.log('系统登录态失效')
          this.getWeixinAppLogin()
        }
      })
      // #endif
    },

    handleAppLogin(e) {
      // 1-小程序登录 weixinMiniProgram
      // 2-微信登录 weixinApp
      // 3-苹果登录 apple
      const { type } = e.target.dataset
      if (['1', '2'].includes(type)) {
        if (this.data.hasWechatInstall) {
          this.getAppLoginCode(type)
        } else {
          wx.showToast({
            icon: 'none',
            title: '需登录微信授权，请先安装微信客户端完成授权',
          })
        }
      } else {
        this.getAppLoginCode(type)
      }
    },

    getAppLoginCode(type) {
      let apiName = 'weixinMiniProgramLogin'
      if (type === '2') apiName = 'weixinAppLogin'
      if (type === '3') apiName = 'appleLogin'

      wx[apiName]({
        success: (res) => {
          const code = res.code
          if (code) {
            this.dountLogin({
              code,
              type
            })
          } else {
            wx.showToast({
              icon: 'none',
              title: err.errMsg || '登录错误，请稍后再试',
            })
          }
        },
        fail: err => {
          console.log(err)
          if (['1', '2'].includes(type) && err.errCode === -700000 && err.errMsg.includes('fail: -700000.sendOpenReq failed:fail opensdk failed')) {
            // {errCode: -700000, errMsg: "wx.weixinMiniProgramLogin fail: -700000.sendOpenReq failed:fail opensdk failed 并没有通过微信返回多端App。"}
            // 用户小程序或微信授权登录跳转微信不授权直接返回app，前端报错提示，苹果审核登录会被拒绝
            return
          }
          wx.showToast({
            icon: 'none',
            title: err.errMsg || '登录错误，请稍后再试',
          })
        }
      })
    },

    getWeixinAppLogin() {
      wx.weixinAppLogin({
        success: (res) => {
          const code = res.code
          if (code) {
            this.dountLogin(code)
          } else {
            wx.showToast({
              icon: 'none',
              title: err.errMsg || '登录错误，请稍后再试',
            })
          }
        },
        fail: err => {
          wx.showToast({
            icon: 'none',
            title: err.errMsg || '登录错误，请稍后再试',
          })
        }
      })
    },

    // 登录
    async onLogin(userInfo) {
      var self=this;
      // let u = userInfo || {}
      // if (u.avatarUrl) delete u.avatarUrl
      // if (u.nickName) delete u.nickName

      let params = {
        js_code: this.data.code,
        userInfo: userInfo
      }
      wx.showLoading({
        title: "正在登录...",
        mask: true
      })
      const res = await app.$api.login(params)
      wx.hideLoading()
      if (res.raw_session) {
        wx.setStorageSync('userSession', res.raw_session);
        await this.getMemberUserInfo()

        const curUser = res.userInfo || {}
        wx.setStorageSync('userInfo', {
          ...curUser,
          isLogin: true
          // ...res.userInfo,
          // avatarUrl: curUser.avatarurl,
          // nickName: curUser.nickname
        })
        wx.showToast({
          icon: 'success',
          title: '登录成功！',
        })
        // 成功回调
        this.data.success && this.data.success()

        // 关闭
        this.setData({
          show: false
        })
      } else {
        // wx.showToast({
        //   title: res.message || '出错了，请稍后再试！',
        // })
        // 关闭
        this.setData({
          show: false
        })
        wx.z.showDialog({
          type: "confirm",
          title: "提示",
          showTitle: false,
          confirmText: "确认",
          //isCoverView: true,
          content: '登录失败,重新登录?',
          success: (res) => {
            wx.removeStorageSync('userInfo');
            wx.removeStorageSync('userSession');
            wx.removeStorageSync('memberUserInfo');
            wx.removeStorageSync('wxLoginInfo');
            self.setData({
              show: true,
              code:""
            })
          }
        })
      }

      // let detail = 'confirm'
      // let option = { bubbles: true, composed: true }

      // const {
      //   success
      // } = this.data
      // success && success({
      //   confirm: true,
      //   cancel: false
      // })
      // this.setData({
      //   show: !this.data.show
      // })
      // this.triggerEvent('confirm', detail, option)
    },

    // dount 授权微信登录
    async dountLogin({ code, type }) {
    // async dountLogin(code) {
      wx.showLoading({
        title: '正在登录...',
        mask: true
      })
      const res = await app.$api.dountwxlogin({
        code,
        type
      })
      wx.hideLoading()

      // code 错误时重新获取 https://dev.weixin.qq.com/docs/framework/dev/openapi/code2Verifyinfo.html
      if (res.code === 10001001) {
        // this.getWeixinAppLogin()
        this.getAppLoginCode(type)
        return
      }
      console.log(res)

      if (res.raw_session) {
        wx.setStorageSync('userSession', res.raw_session);
        await this.getMemberUserInfo()

        const curUser = res.userInfo || {}
        wx.setStorageSync('userInfo', {
          ...curUser,
          isLogin: true
        })
        wx.showToast({
          icon: 'success',
          title: '登录成功！',
        })
        // 成功回调
        this.data.success && this.data.success()

        // 关闭
        this.setData({
          show: false
        })
      } else {
        this.setData({
          show: false
        })
        wx.z.showDialog({
          type: "confirm",
          title: "提示",
          showTitle: false,
          confirmText: "确认",
          content: '登录失败,重新登录?',
          success: (res) => {
            wx.removeStorageSync('userInfo');
            wx.removeStorageSync('userSession');
            wx.removeStorageSync('memberUserInfo');
            wx.removeStorageSync('wxLoginInfo');
            this.setData({
              show: true
            })
          }
        })
      }
    },

    // 取消
    onCancelTap(e) {
      let detail = 'cancel'
      let option = { bubbles: true, composed: true }

      const {
        success
      } = this.data
      success && success({
        confirm: false,
        cancel: true
      })
      Auth.logout(this);
      this.setData({
        show: !this.data.show
      })

      // this.triggerEvent('cancel', detail, option)
    },

    // 关闭
    onClose() {
      // let data = this.data
      // if (!data.maskClosable) return
      this.setData({
        show: false
      })
      this.triggerEvent('close', {}, {})
    },
    stopEvent() {
      // 阻止默认事件
    },

    // 获取会员信息
    async getMemberUserInfo() {
      let params = wx.getStorageSync('userSession') || {}
      const res = await app.$api.getMemberUserInfo(params)
      if (res.memberUserInfo) {
        wx.setStorageSync('memberUserInfo', res.memberUserInfo)
      }
    }
  }
});